"""
Outlook邮件处理模块
"""

import imaplib
import email
import requests
import time

from .common import (
    decode_mime_words,
    normalize_check_time,
    format_date_for_imap_search,
    safe_decode,
    parse_email_date,
    parse_email_message,
    generate_mail_hash,
)
from .logger import (
    logger,
    log_email_start,
    log_email_complete,
    log_email_error,
    log_message_processing,
    log_message_error,
    log_progress,
    timing_decorator
)

class OutlookMailHandler:
    """Outlook邮箱处理类"""

    # Outlook常用文件夹映射
    DEFAULT_FOLDERS = {
        'INBOX': ['inbox', 'Inbox', 'INBOX'],
        'SENT': ['sentitems', 'Sent Items', 'Sent', '已发送'],
        'DRAFTS': ['drafts', 'Drafts', '草稿箱'],
        'TRASH': ['deleteditems', 'Deleted Items', 'Trash', '已删除'],
        'SPAM': ['junkemail', 'Junk E-mail', 'Spam', '垃圾邮件'],
        'ARCHIVE': ['archive', 'Archive', '归档']
    }

    def __init__(self, email_address, access_token):
        """初始化Outlook处理器"""
        self.email_address = email_address
        self.access_token = access_token
        self.mail = None
        self.error = None

    def connect(self, max_retries=3, retry_delay=2):
        """连接到Outlook服务器，增强错误处理和重试机制"""
        import time
        import socket
        import ssl

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试连接Outlook服务器 outlook.office365.com:993 (尝试 {attempt + 1}/{max_retries})")

                # 创建IMAP连接
                self.mail = imaplib.IMAP4_SSL('outlook.office365.com', 993)

                logger.info(f"连接建立成功，开始OAuth2认证: {self.email_address}")

                # 尝试OAuth2认证
                try:
                    auth_string = OutlookMailHandler.generate_auth_string(self.email_address, self.access_token)
                    self.mail.authenticate('XOAUTH2', lambda x: auth_string)
                    logger.info("Outlook OAuth2认证成功")
                    return True

                except imaplib.IMAP4.error as auth_error:
                    error_msg = str(auth_error).lower()

                    if "server logging out" in error_msg or "authentication failed" in error_msg:
                        logger.error(f"Outlook认证失败: {auth_error}")
                        self.error = f"OAuth2认证失败: {auth_error}"

                        # 如果是认证错误，不进行重试
                        if attempt == max_retries - 1:
                            self._provide_outlook_suggestions()
                        break

                    else:
                        logger.error(f"Outlook认证错误: {auth_error}")
                        self.error = str(auth_error)
                        # 对于其他认证错误也提供建议
                        if attempt == max_retries - 1:
                            self._provide_outlook_suggestions()

                # 关闭连接准备重试
                try:
                    self.mail.logout()
                except:
                    pass
                self.mail = None

            except socket.timeout:
                logger.error(f"Outlook连接超时 (尝试 {attempt + 1}/{max_retries})")
                self.error = "连接超时"

            except socket.gaierror as e:
                logger.error(f"Outlook DNS解析失败: {e}")
                self.error = f"无法解析Outlook服务器地址"
                break  # DNS错误不需要重试

            except ConnectionRefusedError:
                logger.error(f"Outlook连接被拒绝")
                self.error = f"Outlook服务器拒绝连接"
                break  # 连接被拒绝不需要重试

            except ssl.SSLError as e:
                logger.error(f"Outlook SSL错误: {e}")
                self.error = f"Outlook SSL连接失败: {e}"
                break  # SSL错误不需要重试

            except Exception as e:
                logger.error(f"Outlook连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                self.error = str(e)

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logger.error(f"Outlook所有连接尝试失败，最终错误: {self.error}")
        return False

    def _provide_outlook_suggestions(self):
        """提供Outlook登录问题的解决建议"""
        suggestions = [
            "检查访问令牌是否已过期",
            "尝试重新获取访问令牌",
            "确认Client ID配置正确",
            "检查Outlook账户是否正常",
            "可能需要使用应用专用密码",
            "设置地址: https://account.microsoft.com/security"
        ]

        logger.info("Outlook邮箱登录失败解决建议:")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"  {i}. {suggestion}")

        self.error += f"\n解决建议: {'; '.join(suggestions)}"

    def get_folders(self):
        """获取文件夹列表"""
        if not self.mail:
            return []

        try:
            _, folders = self.mail.list()
            folder_list = []

            for folder in folders:
                if isinstance(folder, bytes):
                    folder = folder.decode('utf-8', errors='ignore')

                # 解析文件夹名称
                parts = folder.split('"')
                if len(parts) >= 3:
                    folder_name = parts[-2]
                else:
                    folder_name = folder.split()[-1]

                if folder_name and folder_name not in ['.', '..']:
                    folder_list.append(folder_name)

            # 确保常用文件夹在列表中
            default_folders = ['inbox', 'sentitems', 'drafts', 'deleteditems', 'junkemail']
            for df in default_folders:
                if df not in folder_list:
                    folder_list.append(df)

            return sorted(folder_list)
        except Exception as e:
            logger.error(f"获取Outlook文件夹列表失败: {e}")
            return ['inbox']

    def get_messages(self, folder="inbox", limit=100):
        """获取指定文件夹的邮件"""
        if not self.mail:
            return []

        try:
            self.mail.select(folder)
            _, messages = self.mail.search(None, 'ALL')
            message_numbers = messages[0].split()

            # 限制数量并倒序（最新的在前）
            message_numbers = message_numbers[-limit:] if len(message_numbers) > limit else message_numbers
            message_numbers.reverse()

            mail_list = []
            for num in message_numbers:
                try:
                    _, msg_data = self.mail.fetch(num, '(RFC822)')
                    email_body = msg_data[0][1]
                    msg = email.message_from_bytes(email_body)

                    # 简化的邮件解析
                    subject = decode_mime_words(msg.get('Subject', ''))
                    sender = decode_mime_words(msg.get('From', ''))
                    received_time = email.utils.parsedate_to_datetime(msg.get('Date', ''))

                    # 获取邮件内容
                    content = ""
                    if msg.is_multipart():
                        for part in msg.walk():
                            content_type = part.get_content_type()
                            if content_type in ['text/plain', 'text/html']:
                                try:
                                    part_content = part.get_payload(decode=True).decode()
                                    content += part_content
                                except:
                                    pass
                    else:
                        try:
                            content = msg.get_payload(decode=True).decode()
                        except:
                            content = str(msg.get_payload())

                    mail_list.append({
                        'subject': subject,
                        'sender': sender,
                        'received_time': received_time,
                        'content': content,
                        'folder': folder
                    })
                except Exception as e:
                    logger.warning(f"解析Outlook邮件失败: {e}")
                    continue

            return mail_list
        except Exception as e:
            logger.error(f"获取Outlook邮件失败: {e}")
            return []

    def close(self):
        """关闭连接"""
        if self.mail:
            try:
                self.mail.logout()
            except:
                pass
            self.mail = None

    @staticmethod
    def get_new_access_token(refresh_token, client_id):
        """刷新获取新的access_token和refresh_token"""
        url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
        data = {
            'client_id': client_id,
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
        }
        try:
            response = requests.post(url, data=data)
            response_data = response.json()
            result_status = response_data.get('error')
            if result_status is not None:
                logger.error(f"获取访问令牌失败: {result_status}")
                return None, None
            else:
                new_access_token = response_data['access_token']
                # 获取新的refresh_token，如果响应中没有则使用原来的
                new_refresh_token = response_data.get('refresh_token', refresh_token)
                logger.info("成功获取新的访问令牌和刷新令牌")
                return new_access_token, new_refresh_token
        except Exception as e:
            logger.error(f"刷新令牌过程中发生异常: {str(e)}")
            return None, None

    @staticmethod
    def generate_auth_string(user, token):
        """生成 OAuth2 授权字符串"""
        return f"user={user}\1auth=Bearer {token}\1\1"

    @staticmethod
    def fetch_emails(email_address, access_token, folder="inbox", callback=None, last_check_time=None):
        """
        通过IMAP协议获取Outlook/Hotmail邮箱中的邮件

        Args:
            email_address: 邮箱地址
            access_token: OAuth2访问令牌
            folder: 邮件文件夹，默认为收件箱
            callback: 进度回调函数
            last_check_time: 上次检查时间，如果提供，只获取该时间之后的邮件

        Returns:
            list: 邮件记录列表
        """
        mail_records = []

        # 确保回调函数存在
        if callback is None:
            callback = lambda progress, folder: None

        # 标准化处理last_check_time
        last_check_time = normalize_check_time(last_check_time)

        # 使用统一的日志记录格式
        log_email_start(email_address, "未知")
        if last_check_time:
            logger.info(f"获取{folder}文件夹自{last_check_time.isoformat()}以来的新邮件")
        else:
            logger.info(f"获取{folder}文件夹的所有邮件")

        # 尝试连接次数
        max_retries = 3

        for retry in range(max_retries):
            try:
                logger.info(f"尝试连接Outlook邮箱 (尝试 {retry+1}/{max_retries})")
                callback(10, folder)

                # 创建IMAP连接
                mail = imaplib.IMAP4_SSL('outlook.live.com')

                # 使用OAuth2登录
                auth_string = OutlookMailHandler.generate_auth_string(email_address, access_token)
                mail.authenticate('XOAUTH2', lambda x: auth_string)

                # 选择文件夹
                mail.select('inbox')
                callback(20, folder)

                # 定义搜索条件
                if last_check_time:
                    # 确保last_check_time是中国时区的datetime对象
                    normalized_time = normalize_check_time(last_check_time)

                    if normalized_time:
                        # 将上次检查时间转换为IMAP日期格式 (DD-MMM-YYYY)
                        search_date = format_date_for_imap_search(normalized_time)
                        search_cmd = f'(SINCE "{search_date}")'
                        logger.info(f"搜索{search_date}之后的邮件 (原始时间: {last_check_time}, 标准化时间: {normalized_time})")
                        status, data = mail.search(None, search_cmd)
                    else:
                        logger.warning(f"无法标准化检查时间: {last_check_time}，获取所有邮件")
                        status, data = mail.search(None, 'ALL')
                else:
                    # 获取最近的100封邮件
                    status, data = mail.search(None, 'ALL')

                if status != 'OK':
                    logger.error(f"搜索邮件失败: {status}")
                    continue

                # 获取所有邮件ID
                mail_ids = data[0].split()

                total_mails = len(mail_ids)
                logger.info(f"找到{total_mails}封邮件")

                # 处理每封邮件
                for i, mail_id in enumerate(mail_ids):
                    # 更新进度
                    progress = int(20 + (i / total_mails) * 70) if total_mails > 0 else 90
                    callback(progress, folder)

                    try:
                        # 获取邮件
                        status, mail_data = mail.fetch(mail_id, '(RFC822)')

                        if status != 'OK':
                            logger.error(f"获取邮件ID {mail_id} 失败: {status}")
                            continue

                        # 解析邮件
                        msg = email.message_from_bytes(mail_data[0][1])

                        # 解析邮件为结构化数据
                        mail_record = parse_email_message(msg, folder)

                        if not mail_record:
                            logger.warning(f"无法解析邮件ID {mail_id}")
                            continue

                        # 如果有last_check_time，检查邮件时间是否在检查时间之后
                        if last_check_time and mail_record.get('received_time'):
                            normalized_last_check = normalize_check_time(last_check_time)
                            if normalized_last_check and mail_record['received_time'] <= normalized_last_check:
                                logger.debug(f"跳过旧邮件: {mail_record.get('subject', '(无主题)')[:30]}...")
                                continue

                        # 添加到结果列表
                        mail_records.append(mail_record)

                    except Exception as e:
                        log_message_error(mail_id, str(e))

                # 成功获取邮件，跳出重试循环
                callback(90, folder)
                break

            except imaplib.IMAP4.error as e:
                logger.error(f"IMAP错误: {str(e)}")
                time.sleep(1)  # 等待一秒再重试

            except Exception as e:
                logger.error(f"获取邮件异常: {str(e)}")
                time.sleep(1)  # 等待一秒再重试

            finally:
                # 确保关闭连接
                try:
                    mail.logout()
                except:
                    pass

        # 使用统一的完成日志格式
        log_email_complete(email_address, "未知", len(mail_records), len(mail_records), len(mail_records))
        return mail_records

    @staticmethod
    def check_mail(email_info, db, progress_callback=None):
        """检查Outlook/Hotmail邮箱中的邮件并存储到数据库"""
        email_id = email_info['id']
        email_address = email_info['email']
        refresh_token = email_info['refresh_token']
        client_id = email_info['client_id']

        logger.info(f"开始检查Outlook邮箱: ID={email_id}, 邮箱={email_address}")

        # 确保回调函数存在
        if progress_callback is None:
            progress_callback = lambda progress, message: None

        # 报告初始进度
        progress_callback(0, "正在获取访问令牌...")

        try:
            # 记录开始处理
            log_email_start(email_address, email_id)

            # 获取新的访问令牌和刷新令牌
            access_token, new_refresh_token = OutlookMailHandler.get_new_access_token(refresh_token, client_id)
            if not access_token:
                error_msg = "获取访问令牌失败"
                log_email_error(email_address, email_id, error_msg)
                progress_callback(0, error_msg)
                return {
                    'success': False,
                    'message': error_msg
                }

            # 同时更新访问令牌和刷新令牌到数据库
            db.update_email_tokens(email_id, access_token, new_refresh_token)

            # 报告进度
            progress_callback(10, "开始获取邮件...")

            # 获取邮件
            def folder_progress_callback(progress, folder):
                msg = f"正在处理{folder}文件夹，进度{progress}%"
                # 将各文件夹的进度映射到总进度10-90%
                total_progress = 10 + int(progress * 0.8)
                progress_callback(total_progress, msg)

            try:
                mail_records = OutlookMailHandler.fetch_emails(
                    email_address,
                    access_token,
                    "inbox",
                    folder_progress_callback
                )

                # 报告进度
                count = len(mail_records)
                progress_callback(90, f"获取到{count}封邮件，正在保存...")
                logger.info(f"获取到{count}封邮件")

                # 将邮件记录保存到数据库
                saved_count = 0
                for record in mail_records:
                    try:
                        success = db.add_mail_record(
                            email_id,
                            record['subject'],
                            record['sender'],
                            record['received_time'],
                            record['content']
                        )
                        if success:
                            saved_count += 1
                    except Exception as e:
                        logger.error(f"保存邮件记录失败: {str(e)}")

                # 更新最后检查时间
                try:
                    if count >= 0:  # 确保邮件获取成功
                        db.update_check_time(email_id)
                        logger.info(f"已更新最后检查时间")
                    else:
                        logger.warning(f"邮件获取失败，不更新检查时间")
                except Exception as e:
                    logger.error(f"更新检查时间失败: {str(e)}")

                # 使用统一的完成日志格式
                log_email_complete(email_address, email_id, count, count, saved_count)

                # 报告完成
                success_msg = f"成功获取{count}封邮件，新增{saved_count}封"
                progress_callback(100, success_msg)

                return {
                    'success': True,
                    'message': success_msg,
                    'total': count,
                    'saved': saved_count
                }

            except Exception as e:
                error_msg = f"检查邮件失败: {str(e)}"
                log_email_error(email_address, email_id, error_msg)
                progress_callback(0, error_msg)
                return {
                    'success': False,
                    'message': error_msg
                }

        except Exception as e:
            error_msg = f"处理邮箱过程中出错: {str(e)}"
            log_email_error(email_address, email_id, error_msg)
            progress_callback(0, error_msg)
            return {
                'success': False,
                'message': error_msg
            }