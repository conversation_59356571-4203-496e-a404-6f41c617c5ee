"""
IMAP邮件处理模块 - 增强版
提供更丰富的日志记录、错误检测和进度通知
"""

import imaplib
import email
from email.header import decode_header
from email.utils import parsedate_to_datetime
import os
import logging
from datetime import datetime
import threading
import socket
import ssl
import time
import traceback
from typing import List, Dict, Optional, Callable

from .common import (
    decode_mime_words,
    parse_email_date,
    decode_email_content,
    parse_email_message,
    extract_email_content,
    normalize_check_time,
    format_date_for_imap_search
)
from .logger import (
    logger,
    log_email_start,
    log_email_complete,
    log_email_error,
    log_message_processing,
    log_message_error,
    log_progress,
    timing_decorator
)

logger = logging.getLogger(__name__)

class IMAPMailHandler:
    """IMAP邮箱处理类 - 增强版"""

    # 常用文件夹映射
    DEFAULT_FOLDERS = {
        'INBOX': ['INBOX', 'Inbox', 'inbox'],
        'SENT': ['Sent', 'SENT', 'Sent Items', 'Sent Messages', '已发送'],
        'DRAFTS': ['Drafts', 'DRAFTS', 'Draft', '草稿箱'],
        'TRASH': ['Trash', 'TRASH', 'Deleted', 'Deleted Items', 'Deleted Messages', '垃圾箱', '已删除'],
        'SPAM': ['Spam', 'SPAM', 'Junk', 'Junk E-mail', 'Bulk Mail', '垃圾邮件'],
        'ARCHIVE': ['Archive', 'ARCHIVE', 'All Mail', '归档']
    }

    def __init__(self, server, username, password, use_ssl=True, port=None):
        """初始化IMAP处理器"""
        self.server = server
        self.username = username
        self.password = password
        self.use_ssl = use_ssl
        self.port = port or (993 if use_ssl else 143)
        self.mail = None
        self.error = None

        # 自动检测服务器
        if not server and '@' in username:
            domain = username.split('@')[1].lower()
            if 'gmail' in domain:
                self.server = 'imap.gmail.com'
            elif 'qq.com' in domain:
                self.server = 'imap.qq.com'
            elif 'outlook' in domain or 'hotmail' in domain or 'live' in domain:
                self.server = 'outlook.office365.com'
            elif '163.com' in domain:
                self.server = 'imap.163.com'
            elif '126.com' in domain:
                self.server = 'imap.126.com'

    def connect(self, max_retries=3, retry_delay=2):
        """连接到IMAP服务器，增强错误处理和重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试连接IMAP服务器 {self.server}:{self.port} (尝试 {attempt + 1}/{max_retries})")

                # 创建连接
                if self.use_ssl:
                    self.mail = imaplib.IMAP4_SSL(self.server, self.port)
                else:
                    self.mail = imaplib.IMAP4(self.server, self.port)

                logger.info(f"连接建立成功，开始登录: {self.username}")

                # 尝试登录
                try:
                    self.mail.login(self.username, self.password)
                    logger.info("IMAP登录成功")
                    return True

                except imaplib.IMAP4.error as login_error:
                    error_msg = str(login_error).lower()

                    if "server logging out" in error_msg:
                        logger.error(f"IMAP登录被拒绝: {login_error}")
                        self.error = f"登录被拒绝: {login_error}"

                        # 尝试不同的用户名格式
                        if '@' in self.username and attempt == 0:
                            logger.info("尝试使用用户名部分登录")
                            username_only = self.username.split('@')[0]
                            try:
                                self.mail.login(username_only, self.password)
                                logger.info("使用用户名部分登录成功")
                                self.username = username_only  # 更新用户名
                                return True
                            except Exception as e2:
                                logger.warning(f"用户名部分登录也失败: {e2}")

                        # 如果是认证错误，不进行重试
                        if attempt == max_retries - 1:
                            self._provide_login_suggestions()
                        break

                    elif "authentication failed" in error_msg or "invalid credentials" in error_msg:
                        logger.error(f"认证失败: {login_error}")
                        self.error = f"认证失败: {login_error}"
                        self._provide_login_suggestions()
                        break

                    else:
                        logger.error(f"登录错误: {login_error}")
                        self.error = str(login_error)

                except Exception as login_error:
                    logger.error(f"登录异常: {login_error}")
                    self.error = str(login_error)

                # 关闭连接准备重试
                try:
                    self.mail.logout()
                except:
                    pass
                self.mail = None

            except socket.timeout:
                logger.error(f"连接超时 (尝试 {attempt + 1}/{max_retries})")
                self.error = "连接超时"

            except socket.gaierror as e:
                logger.error(f"DNS解析失败: {e}")
                self.error = f"无法解析服务器地址: {self.server}"
                break  # DNS错误不需要重试

            except ConnectionRefusedError:
                logger.error(f"连接被拒绝: {self.server}:{self.port}")
                self.error = f"服务器拒绝连接: {self.server}:{self.port}"
                break  # 连接被拒绝不需要重试

            except ssl.SSLError as e:
                logger.error(f"SSL错误: {e}")
                self.error = f"SSL连接失败: {e}"
                break  # SSL错误不需要重试

            except Exception as e:
                logger.error(f"连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                self.error = str(e)

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logger.error(f"所有连接尝试失败，最终错误: {self.error}")
        return False

    def _provide_login_suggestions(self):
        """提供登录问题的解决建议"""
        domain = self.username.split('@')[1].lower() if '@' in self.username else ''

        suggestions = []
        if 'gmail' in domain:
            suggestions.append("Gmail需要开启'不够安全的应用的访问权限'或使用应用专用密码")
        elif 'qq.com' in domain:
            suggestions.append("QQ邮箱需要在设置中开启IMAP服务并使用授权码代替密码")
        elif 'outlook' in domain or 'hotmail' in domain or 'live' in domain:
            suggestions.append("Outlook邮箱可能需要使用应用专用密码")
        elif '163.com' in domain or '126.com' in domain:
            suggestions.append("网易邮箱需要在设置中开启IMAP服务并使用授权码")

        suggestions.extend([
            "检查用户名和密码是否正确",
            "确认IMAP服务已在邮箱设置中启用",
            "检查是否需要使用应用专用密码或授权码",
            "确认服务器地址和端口配置正确"
        ])

        logger.info("登录失败解决建议:")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"  {i}. {suggestion}")

        self.error += f"\n解决建议: {'; '.join(suggestions)}"

    def get_folders(self):
        """获取文件夹列表"""
        if not self.mail:
            return []

        try:
            _, folders = self.mail.list()
            folder_list = []

            for folder in folders:
                if isinstance(folder, bytes):
                    folder = folder.decode('utf-8', errors='ignore')

                # 解析文件夹名称
                parts = folder.split('"')
                if len(parts) >= 3:
                    folder_name = parts[-2]
                else:
                    # 简单解析
                    folder_name = folder.split()[-1]

                if folder_name and folder_name not in ['.', '..']:
                    folder_list.append(folder_name)

            # 确保常用文件夹在列表中
            default_folders = ['INBOX', 'Sent', 'Drafts', 'Trash', 'Spam']
            for df in default_folders:
                if df not in folder_list:
                    folder_list.append(df)

            return sorted(folder_list)
        except Exception as e:
            logger.error(f"获取文件夹列表失败: {e}")
            return ['INBOX']

    def get_messages(self, folder="INBOX", limit=100):
        """获取指定文件夹的邮件"""
        if not self.mail:
            return []

        try:
            self.mail.select(folder)
            _, messages = self.mail.search(None, 'ALL')
            message_numbers = messages[0].split()

            # 限制数量并倒序（最新的在前）
            message_numbers = message_numbers[-limit:] if len(message_numbers) > limit else message_numbers
            message_numbers.reverse()

            mail_list = []
            for num in message_numbers:
                try:
                    _, msg_data = self.mail.fetch(num, '(RFC822)')
                    email_body = msg_data[0][1]
                    msg = email.message_from_bytes(email_body)

                    mail_record = parse_email_message(msg, folder)
                    if mail_record:
                        mail_list.append(mail_record)
                except Exception as e:
                    logger.warning(f"解析邮件失败: {e}")
                    continue

            return mail_list
        except Exception as e:
            logger.error(f"获取邮件失败: {e}")
            return []

    def close(self):
        """关闭连接"""
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
            except:
                pass
            self.mail = None

    @staticmethod
    def _batch_fetch_mail_info(mail, message_numbers, batch_size=50):
        """
        批量获取邮件头信息，包括Message-ID和Server-UID

        Args:
            mail: IMAP连接对象
            message_numbers: 邮件序号列表
            batch_size: 批量处理大小

        Returns:
            dict: 邮件信息字典，键为邮件序号，值为邮件头信息
        """
        mail_info_dict = {}

        try:
            for i in range(0, len(message_numbers), batch_size):
                batch_numbers = message_numbers[i:i + batch_size]

                # 构建批量查询范围
                if len(batch_numbers) == 1:
                    id_range = batch_numbers[0].decode() if isinstance(batch_numbers[0], bytes) else str(batch_numbers[0])
                else:
                    start_id = batch_numbers[0].decode() if isinstance(batch_numbers[0], bytes) else str(batch_numbers[0])
                    end_id = batch_numbers[-1].decode() if isinstance(batch_numbers[-1], bytes) else str(batch_numbers[-1])
                    id_range = f"{start_id}:{end_id}"

                # 批量获取UID和头信息
                _, fetch_data = mail.fetch(id_range, '(UID BODY.PEEK[HEADER.FIELDS (SUBJECT FROM DATE MESSAGE-ID)])')

                # 解析批量响应
                for item in fetch_data:
                    if isinstance(item, tuple) and len(item) >= 2:
                        # 解析响应头
                        response_line = item[0].decode() if isinstance(item[0], bytes) else str(item[0])
                        header_data = item[1]

                        # 提取邮件序号和UID
                        import re
                        match = re.search(r'(\d+) \(UID (\d+)', response_line)
                        if match:
                            mail_num = match.group(1)
                            server_uid = match.group(2)

                            # 解析邮件头
                            if header_data:
                                try:
                                    msg = email.message_from_bytes(header_data)
                                    mail_info_dict[mail_num] = {
                                        'server_uid': server_uid,
                                        'message_id': msg.get('Message-ID', '').strip('<>'),
                                        'subject': msg.get('Subject', ''),
                                        'sender': msg.get('From', ''),
                                        'date': msg.get('Date', '')
                                    }
                                except Exception as e:
                                    logger.warning(f"解析邮件头失败: {str(e)}")

        except Exception as e:
            logger.error(f"批量获取邮件信息失败: {str(e)}")

        return mail_info_dict

    @staticmethod
    @timing_decorator
    def fetch_emails(email_address, password, server, port=993, use_ssl=True, folder="INBOX", callback=None, last_check_time=None, db=None, email_id=None):
        """获取邮箱中的邮件"""
        mail_records = []
        mail = None

        try:
            # 创建回调函数
            if callback is None:
                callback = lambda progress, message: None

            # 标准化处理last_check_time
            last_check_time = normalize_check_time(last_check_time)

            if last_check_time:
                logger.info(f"获取自 {last_check_time.isoformat()} 以来的新邮件")
            else:
                logger.info(f"获取所有邮件")

            # 连接IMAP服务器
            logger.info(f"连接IMAP服务器 {server}:{port} (SSL: {use_ssl})")
            if callback:
                callback(0, "正在连接邮箱服务器")

            if use_ssl:
                mail = imaplib.IMAP4_SSL(server, port)
            else:
                mail = imaplib.IMAP4(server, port)

            # 登录
            logger.info(f"登录邮箱 {email_address}")
            if callback:
                callback(10, "正在登录邮箱")

            mail.login(email_address, password)

            # 选择邮件文件夹
            logger.info(f"选择文件夹 {folder}")
            if callback:
                callback(20, f"正在选择文件夹 {folder}")

            mail.select(folder)

            # 搜索邮件
            search_criteria = 'ALL'

            # 如果提供了上次检查时间，只获取新邮件
            if last_check_time:
                # 标准化处理上次检查时间
                normalized_time = normalize_check_time(last_check_time)
                if normalized_time:
                    # 将日期转换成IMAP搜索格式 (DD-MMM-YYYY)
                    date_str = format_date_for_imap_search(normalized_time)
                    if date_str:
                        search_criteria = f'SINCE {date_str}'
                        logger.info(f"搜索{date_str}之后的邮件")
                        logger.debug(f"原始检查时间: {last_check_time}, 标准化时间: {normalized_time}, IMAP格式: {date_str}")
                    else:
                        logger.warning(f"无法格式化日期为IMAP格式: {normalized_time}")
                else:
                    logger.warning(f"无法标准化检查时间: {last_check_time}")

            _, messages = mail.search(None, search_criteria)
            message_numbers = messages[0].split()
            total_messages = len(message_numbers)

            logger.info(f"找到 {total_messages} 封邮件")

            # 如果没有找到邮件，直接返回空列表
            if total_messages == 0:
                logger.info("没有找到新邮件，跳过处理")
                mail.close()
                mail.logout()
                return []

            # 三阶段处理流程
            logger.info(f"开始三阶段邮件同步处理，总邮件数: {total_messages}")

            # 第一阶段：批量获取邮件头信息
            if callback:
                callback(10, "第一阶段：批量获取邮件头信息")

            mail_info_dict = IMAPMailHandler._batch_fetch_mail_info(mail, message_numbers)
            logger.info(f"第一阶段完成：获取到 {len(mail_info_dict)} 封邮件的头信息")

            # 第二阶段：预检查过滤（如果提供了数据库连接和邮箱ID）
            new_mails_to_process = []
            if db and email_id:
                if callback:
                    callback(30, "第二阶段：预检查过滤已存在邮件")

                from .mail_precheck import MailPreChecker
                precheck = MailPreChecker(db)

                # 构建预检查用的邮件列表
                precheck_mails = []
                for num in message_numbers:
                    num_str = num.decode() if isinstance(num, bytes) else str(num)
                    if num_str in mail_info_dict:
                        info = mail_info_dict[num_str]
                        # 解析日期
                        received_time = None
                        if info.get('date'):
                            try:
                                from email.utils import parsedate_to_datetime
                                received_time = parsedate_to_datetime(info['date'])
                            except:
                                from .common import get_china_now
                                received_time = get_china_now()

                        precheck_mails.append({
                            'mail_num': num_str,
                            'message_id': info.get('message_id'),
                            'server_uid': info.get('server_uid'),
                            'subject': info.get('subject'),
                            'sender': info.get('sender'),
                            'received_time': received_time
                        })

                # 过滤出新邮件
                new_mails_info = precheck.filter_new_mails(email_id, precheck_mails)
                new_mails_to_process = [mail['mail_num'] for mail in new_mails_info]

                logger.info(f"第二阶段完成：过滤后需要处理 {len(new_mails_to_process)} 封新邮件")
            else:
                # 如果没有数据库连接，处理所有邮件
                new_mails_to_process = [num.decode() if isinstance(num, bytes) else str(num) for num in message_numbers]
                logger.info("未提供数据库连接，将处理所有邮件")

            # 第三阶段：只对新邮件获取完整内容
            if callback:
                callback(50, f"第三阶段：获取 {len(new_mails_to_process)} 封新邮件的完整内容")

            for i, mail_num in enumerate(new_mails_to_process):
                try:
                    # 更新进度
                    progress = 50 + int((i + 1) / len(new_mails_to_process) * 50)
                    if callback:
                        callback(progress, f"正在处理第 {i + 1}/{len(new_mails_to_process)} 封新邮件")

                    # 获取完整邮件内容
                    _, msg_data = mail.fetch(mail_num, '(RFC822)')
                    email_body = msg_data[0][1]

                    # 尝试使用标准方式解析邮件
                    try:
                        msg = email.message_from_bytes(email_body)
                        mail_record = parse_email_message(msg, folder)
                    except Exception as e:
                        logger.warning(f"标准方式解析邮件失败，尝试使用EML解析器: {str(e)}")
                        mail_record = None

                    # 如果标准解析失败，尝试使用EML解析器
                    if not mail_record:
                        try:
                            from .file_parser import EmailFileParser
                            logger.info("使用EML解析器解析邮件")
                            mail_record = EmailFileParser.parse_eml_content(email_body)
                            if mail_record:
                                # 设置文件夹信息
                                mail_record['folder'] = folder
                        except Exception as e:
                            logger.error(f"EML解析器解析邮件失败: {str(e)}")
                            mail_record = None

                    if mail_record:
                        # 添加message_id和server_uid到邮件记录中
                        if mail_num in mail_info_dict:
                            info = mail_info_dict[mail_num]
                            mail_record['message_id'] = info.get('message_id')
                            mail_record['server_uid'] = info.get('server_uid')

                        # 记录邮件处理信息
                        mail_records.append(mail_record)
                        message_id = mail_record.get('message_id', 'unknown')
                        subject = mail_record.get('subject', '(无主题)')
                        log_message_processing(message_id, i+1, len(new_mails_to_process), subject)
                    else:
                        logger.error(f"无法解析邮件 {mail_num}")

                except Exception as e:
                    logger.error(f"处理邮件失败: {str(e)}")
                    message_id = 'unknown'
                    log_message_error(message_id, str(e))
                    continue

            # 关闭连接
            mail.close()
            mail.logout()

            # 记录完成日志
            log_email_complete(email_address, "未知", len(mail_records), len(mail_records), len(mail_records))

            return mail_records

        except Exception as e:
            logger.error(f"获取邮件失败: {str(e)}")
            log_email_error(email_address, "未知", str(e))
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass
            return []

    @staticmethod
    @timing_decorator
    def check_mail(email_info, db, progress_callback=None):
        """检查邮箱中的新邮件"""
        try:
            email_address = email_info['email']
            password = email_info['password']
            server = email_info.get('server', 'imap.gmail.com')
            port = email_info.get('port', 993)
            use_ssl = email_info.get('use_ssl', True)
            last_check_time = email_info.get('last_check_time')

            # 创建进度回调
            def folder_progress_callback(progress, folder):
                if progress_callback:
                    progress_callback(progress, f"正在检查文件夹: {folder}")

            # 获取邮件，传递last_check_time参数以实现增量同步
            mail_records = IMAPMailHandler.fetch_emails(
                email_address=email_address,
                password=password,
                server=server,
                port=port,
                use_ssl=use_ssl,
                callback=folder_progress_callback,
                last_check_time=last_check_time
            )

            if not mail_records:
                if progress_callback:
                    progress_callback(100, "没有找到新邮件")
                return {'success': True, 'message': '没有找到新邮件'}

            # 保存邮件记录
            saved_count = db.save_mail_records(email_info['id'], mail_records, progress_callback)

            if progress_callback:
                progress_callback(100, f"成功获取 {len(mail_records)} 封邮件，新增 {saved_count} 封")

            return {
                'success': True,
                'message': f'成功获取 {len(mail_records)} 封邮件，新增 {saved_count} 封'
            }

        except Exception as e:
            logger.error(f"检查邮件失败: {str(e)}")
            if progress_callback:
                progress_callback(0, f"检查邮件失败: {str(e)}")
            return {'success': False, 'message': str(e)}

